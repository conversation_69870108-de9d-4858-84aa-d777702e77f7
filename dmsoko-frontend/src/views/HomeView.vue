<script setup>
import { ref, onMounted } from 'vue'
import { useListingsStore } from '@/stores/listings'

const listingsStore = useListingsStore()
const searchQuery = ref('')

onMounted(async () => {
  // Fetch initial data for home page
  try {
    await Promise.all([
      listingsStore.fetchListings({ page_size: 12 }),
      listingsStore.fetchCategories()
    ])
  } catch (error) {
    console.log('Error fetching data:', error)
  }
})

const handleSearch = () => {
  console.log('Searching for:', searchQuery.value)
}
</script>

<template>
  <div>
    <!-- Hero Section -->
    <section style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 4rem 2rem; text-align: center;">
      <h1 style="font-size: 3rem; font-weight: bold; margin-bottom: 1rem;">
        Find What You Need, Sell What You Don't
      </h1>
      <p style="font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9;">
        Join thousands of buyers and sellers in the most trusted marketplace
      </p>

      <!-- Search Bar -->
      <div style="max-width: 600px; margin: 0 auto; display: flex; gap: 1rem;">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="What are you looking for?"
          style="flex: 1; padding: 1rem; border: none; border-radius: 8px; font-size: 1rem;"
          @keyup.enter="handleSearch"
        >
        <button
          @click="handleSearch"
          style="background: #fbbf24; color: #1f2937; padding: 1rem 2rem; border: none; border-radius: 8px; font-weight: bold; cursor: pointer;"
        >
          Search
        </button>
      </div>
    </section>

    <!-- Categories -->
    <section style="padding: 3rem 2rem; background: white;">
      <h2 style="text-align: center; font-size: 2rem; font-weight: bold; margin-bottom: 2rem;">
        Browse by Category
      </h2>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; max-width: 1000px; margin: 0 auto;">
        <div
          v-for="category in listingsStore.categories.slice(0, 8)"
          :key="category.name"
          style="text-align: center; padding: 2rem; border: 2px solid #e5e7eb; border-radius: 12px; cursor: pointer; transition: all 0.3s;"
          @mouseover="$event.target.style.borderColor = '#3b82f6'"
          @mouseleave="$event.target.style.borderColor = '#e5e7eb'"
        >
          <div style="font-size: 3rem; margin-bottom: 1rem;">{{ category.icon || '📦' }}</div>
          <h3 style="font-weight: bold; color: #1f2937;">{{ category.category_name }}</h3>
          <p style="color: #6b7280; font-size: 0.9rem; margin-top: 0.5rem;">{{ category.description }}</p>
        </div>
      </div>
    </section>

    <!-- Recent Listings -->
    <section style="padding: 3rem 2rem; background: #f9fafb;">
      <h2 style="text-align: center; font-size: 2rem; font-weight: bold; margin-bottom: 2rem;">
        Latest Listings
      </h2>

      <div v-if="listingsStore.loading" style="text-align: center; padding: 2rem;">
        <p>Loading listings...</p>
      </div>

      <div v-else-if="listingsStore.listings.length === 0" style="text-align: center; padding: 2rem;">
        <div style="font-size: 4rem; margin-bottom: 1rem;">📦</div>
        <h3 style="font-size: 1.5rem; margin-bottom: 1rem;">No Listings Yet</h3>
        <p style="color: #6b7280; margin-bottom: 2rem;">Be the first to post a listing on our marketplace!</p>
        <router-link
          to="/post-listing"
          style="background: #3b82f6; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold;"
        >
          Post Your Ad
        </router-link>
      </div>

      <div v-else style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; max-width: 1200px; margin: 0 auto;">
        <div
          v-for="listing in listingsStore.listings.slice(0, 8)"
          :key="listing.name"
          style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); transition: transform 0.3s;"
          @mouseover="$event.target.style.transform = 'translateY(-4px)'"
          @mouseleave="$event.target.style.transform = 'translateY(0)'"
        >
          <div style="height: 200px; background: #e5e7eb; display: flex; align-items: center; justify-content: center;">
            <span style="color: #6b7280; font-size: 3rem;">📷</span>
          </div>
          <div style="padding: 1.5rem;">
            <h3 style="font-weight: bold; margin-bottom: 0.5rem; color: #1f2937;">{{ listing.title }}</h3>
            <p style="color: #3b82f6; font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem;">
              ${{ listing.price || 'Contact for Price' }}
            </p>
            <div style="display: flex; justify-content: space-between; align-items: center; color: #6b7280; font-size: 0.9rem;">
              <span>{{ listing.location || 'Location not specified' }}</span>
              <span>{{ new Date(listing.created_at).toLocaleDateString() }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section style="background: #3b82f6; color: white; padding: 3rem 2rem; text-align: center;">
      <h2 style="font-size: 2rem; font-weight: bold; margin-bottom: 2rem;">
        Join Our Growing Community
      </h2>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem; max-width: 800px; margin: 0 auto;">
        <div>
          <div style="font-size: 3rem; font-weight: bold; margin-bottom: 0.5rem;">1,250+</div>
          <div style="opacity: 0.9;">Active Listings</div>
        </div>
        <div>
          <div style="font-size: 3rem; font-weight: bold; margin-bottom: 0.5rem;">850+</div>
          <div style="opacity: 0.9;">Registered Users</div>
        </div>
        <div>
          <div style="font-size: 3rem; font-weight: bold; margin-bottom: 0.5rem;">{{ listingsStore.categories.length || 8 }}+</div>
          <div style="opacity: 0.9;">Categories</div>
        </div>
        <div>
          <div style="font-size: 3rem; font-weight: bold; margin-bottom: 0.5rem;">2,100+</div>
          <div style="opacity: 0.9;">Successful Deals</div>
        </div>
      </div>
    </section>
  </div>
</template>
