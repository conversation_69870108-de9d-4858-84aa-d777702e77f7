<script setup>
import { onMounted } from 'vue'
import { useListingsStore } from '@/stores/listings'
import HeroSection from '@/components/home/<USER>'
import CategoryGrid from '@/components/home/<USER>'
import FeaturedListings from '@/components/home/<USER>'
import RecentListings from '@/components/home/<USER>'
import StatsSection from '@/components/home/<USER>'

const listingsStore = useListingsStore()

onMounted(async () => {
  // Fetch initial data for home page
  await Promise.all([
    listingsStore.fetchListings({ page_size: 12 }),
    listingsStore.fetchCategories()
  ])
})
</script>

<template>
  <div>
    <!-- Hero Section -->
    <HeroSection />

    <!-- Categories -->
    <CategoryGrid />

    <!-- Featured Listings -->
    <FeaturedListings />

    <!-- Recent Listings -->
    <RecentListings />

    <!-- Stats Section -->
    <StatsSection />
  </div>
</template>
