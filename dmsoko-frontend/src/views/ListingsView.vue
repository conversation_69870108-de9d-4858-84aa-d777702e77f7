<template>
  <div style="min-height: 100vh; background: #f9fafb;">
    <!-- Page Header -->
    <div style="background: white; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); padding: 2rem;">
      <div style="max-width: 1200px; margin: 0 auto;">
        <h1 style="font-size: 2rem; font-weight: bold; color: #1f2937; margin-bottom: 0.5rem;">Browse Listings</h1>
        <p style="color: #6b7280;">
          {{ totalListings }} listings found
          <span v-if="activeFilters.length > 0">with your filters</span>
        </p>
      </div>
    </div>

    <div style="max-width: 1200px; margin: 0 auto; padding: 2rem;">
      <!-- Search and Filters -->
      <div style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); margin-bottom: 2rem;">
        <div style="display: flex; gap: 1rem; flex-wrap: wrap; align-items: center;">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search listings..."
            style="flex: 1; min-width: 300px; padding: 0.75rem; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 1rem;"
            @input="handleSearch"
          >
          <select
            v-model="selectedCategory"
            @change="handleCategoryChange"
            style="padding: 0.75rem; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 1rem; min-width: 200px;"
          >
            <option value="">All Categories</option>
            <option
              v-for="category in listingsStore.categories"
              :key="category.name"
              :value="category.name"
            >
              {{ category.category_name }}
            </option>
          </select>
          <select
            v-model="sortBy"
            @change="handleSortChange"
            style="padding: 0.75rem; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 1rem; min-width: 180px;"
          >
            <option value="created_at:desc">Newest First</option>
            <option value="created_at:asc">Oldest First</option>
            <option value="price:asc">Price: Low to High</option>
            <option value="price:desc">Price: High to Low</option>
            <option value="title:asc">Title: A to Z</option>
          </select>
        </div>
      </div>

      <!-- Active Filters -->
      <div v-if="activeFilters.length > 0" style="margin-bottom: 2rem;">
        <div style="display: flex; flex-wrap: wrap; gap: 0.5rem; align-items: center;">
          <span style="font-weight: 500; color: #374151;">Active filters:</span>
          <div
            v-for="filter in activeFilters"
            :key="filter.key"
            style="display: inline-flex; align-items: center; gap: 0.5rem; background: #dbeafe; color: #1e40af; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;"
          >
            <span>{{ filter.label }}</span>
            <button
              @click="removeFilter(filter.key)"
              style="background: none; border: none; color: #1e40af; cursor: pointer; font-size: 1.2rem;"
            >
              ×
            </button>
          </div>
          <button
            @click="clearFilters"
            style="background: none; border: none; color: #6b7280; text-decoration: underline; cursor: pointer;"
          >
            Clear all
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="listingsStore.loading" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
        <div
          v-for="n in 9"
          :key="n"
          style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
        >
          <div style="height: 200px; background: #e5e7eb; animation: pulse 2s infinite;"></div>
          <div style="padding: 1.5rem;">
            <div style="height: 1rem; background: #e5e7eb; border-radius: 4px; margin-bottom: 0.5rem; animation: pulse 2s infinite;"></div>
            <div style="height: 1rem; background: #e5e7eb; border-radius: 4px; width: 75%; animation: pulse 2s infinite;"></div>
          </div>
        </div>
      </div>

      <!-- No Results -->
      <div v-else-if="listings.length === 0" style="text-align: center; padding: 3rem;">
        <div style="font-size: 4rem; margin-bottom: 1rem;">🔍</div>
        <h3 style="font-size: 1.5rem; font-weight: bold; color: #1f2937; margin-bottom: 1rem;">No listings found</h3>
        <p style="color: #6b7280; margin-bottom: 2rem;">
          Try adjusting your filters or search terms to find what you're looking for.
        </p>
        <button
          @click="clearFilters"
          style="background: #3b82f6; color: white; padding: 1rem 2rem; border: none; border-radius: 8px; font-weight: bold; cursor: pointer;"
        >
          Clear Filters
        </button>
      </div>

      <!-- Listings Grid -->
      <div v-else style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
        <div
          v-for="listing in listings"
          :key="listing.name"
          style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); transition: transform 0.3s;"
          @mouseover="$event.target.style.transform = 'translateY(-4px)'"
          @mouseleave="$event.target.style.transform = 'translateY(0)'"
        >
          <div style="height: 200px; background: #e5e7eb; display: flex; align-items: center; justify-content: center;">
            <span style="color: #6b7280; font-size: 3rem;">📷</span>
          </div>
          <div style="padding: 1.5rem;">
            <h3 style="font-weight: bold; margin-bottom: 0.5rem; color: #1f2937;">{{ listing.title }}</h3>
            <p style="color: #3b82f6; font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem;">
              ${{ listing.price || 'Contact for Price' }}
            </p>
            <div style="display: flex; justify-content: space-between; align-items: center; color: #6b7280; font-size: 0.9rem;">
              <span>{{ listing.location || 'Location not specified' }}</span>
              <span>{{ new Date(listing.created_at).toLocaleDateString() }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Simple Pagination -->
      <div v-if="totalPages > 1" style="margin-top: 3rem; text-align: center;">
        <div style="display: inline-flex; gap: 0.5rem; align-items: center;">
          <button
            @click="goToPrevious"
            :disabled="currentPage <= 1"
            style="padding: 0.5rem 1rem; border: 2px solid #e5e7eb; background: white; border-radius: 8px; cursor: pointer;"
            :style="{ opacity: currentPage <= 1 ? 0.5 : 1, cursor: currentPage <= 1 ? 'not-allowed' : 'pointer' }"
          >
            Previous
          </button>

          <span style="padding: 0.5rem 1rem; color: #6b7280;">
            Page {{ currentPage }} of {{ totalPages }}
          </span>

          <button
            @click="goToNext"
            :disabled="currentPage >= totalPages"
            style="padding: 0.5rem 1rem; border: 2px solid #e5e7eb; background: white; border-radius: 8px; cursor: pointer;"
            :style="{ opacity: currentPage >= totalPages ? 0.5 : 1, cursor: currentPage >= totalPages ? 'not-allowed' : 'pointer' }"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useListingsStore } from '@/stores/listings'

const listingsStore = useListingsStore()

const searchQuery = ref('')
const selectedCategory = ref('')
const sortBy = ref('created_at:desc')

const listings = computed(() => listingsStore.listings)
const totalListings = computed(() => listingsStore.pagination.totalCount || 0)
const currentPage = computed(() => listingsStore.pagination.page || 1)
const totalPages = computed(() => listingsStore.pagination.totalPages || 1)

const activeFilters = computed(() => {
  const filters = []

  if (searchQuery.value) {
    filters.push({ key: 'search', label: `Search: ${searchQuery.value}` })
  }
  if (selectedCategory.value) {
    const category = listingsStore.categories.find(c => c.name === selectedCategory.value)
    filters.push({ key: 'category', label: `Category: ${category?.category_name || selectedCategory.value}` })
  }

  return filters
})

const handleSearch = () => {
  console.log('Searching for:', searchQuery.value)
  fetchListings()
}

const handleCategoryChange = () => {
  console.log('Category changed to:', selectedCategory.value)
  fetchListings()
}

const handleSortChange = () => {
  console.log('Sort changed to:', sortBy.value)
  fetchListings()
}

const removeFilter = (filterKey) => {
  if (filterKey === 'search') {
    searchQuery.value = ''
  } else if (filterKey === 'category') {
    selectedCategory.value = ''
  }
  fetchListings()
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  sortBy.value = 'created_at:desc'
  fetchListings()
}

const goToPrevious = () => {
  if (currentPage.value > 1) {
    listingsStore.setPage(currentPage.value - 1)
    fetchListings()
  }
}

const goToNext = () => {
  if (currentPage.value < totalPages.value) {
    listingsStore.setPage(currentPage.value + 1)
    fetchListings()
  }
}

const fetchListings = async () => {
  try {
    const filters = {}

    if (searchQuery.value.trim()) {
      filters.search = searchQuery.value.trim()
    }

    if (selectedCategory.value) {
      filters.category = selectedCategory.value
    }

    const [field, order] = sortBy.value.split(':')
    filters.sortBy = field
    filters.sortOrder = order

    listingsStore.setFilters(filters)
    await listingsStore.fetchListings()
  } catch (error) {
    console.error('Error fetching listings:', error)
  }
}

onMounted(async () => {
  try {
    // Initialize categories if not already loaded
    if (listingsStore.categories.length === 0) {
      await listingsStore.fetchCategories()
    }

    // Fetch initial listings
    await fetchListings()
  } catch (error) {
    console.error('Error initializing listings page:', error)
  }
})
</script>
