<script setup>
import { RouterView } from 'vue-router'
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

onMounted(() => {
  // Initialize auth state from localStorage
  authStore.initializeAuth()
})
</script>

<template>
  <div id="app">
    <nav style="background: #3b82f6; color: white; padding: 1rem;">
      <div style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
        <h1 style="font-size: 1.5rem; font-weight: bold;">DMSoko</h1>
        <div>
          <router-link to="/" style="color: white; text-decoration: none; margin-right: 1rem;">Home</router-link>
          <router-link to="/listings" style="color: white; text-decoration: none; margin-right: 1rem;">Listings</router-link>
          <router-link to="/about" style="color: white; text-decoration: none;">About</router-link>
        </div>
      </div>
    </nav>

    <main style="min-height: 80vh; padding: 2rem;">
      <RouterView />
    </main>

    <footer style="background: #1f2937; color: white; padding: 2rem; text-align: center;">
      <p>&copy; 2025 DMSoko. All rights reserved.</p>
    </footer>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}
</style>
