import { defineStore } from 'pinia'
import { listingsAPI, categoriesAPI } from '@/services/api'

export const useListingsStore = defineStore('listings', {
  state: () => ({
    listings: [],
    currentListing: null,
    categories: [],
    filters: {
      category: null,
      search: '',
      location: '',
      minPrice: null,
      maxPrice: null,
      sortBy: 'created_at',
      sortOrder: 'desc'
    },
    pagination: {
      page: 1,
      pageSize: 20,
      totalCount: 0,
      totalPages: 0
    },
    loading: false,
    error: null
  }),

  getters: {
    filteredListings: (state) => {
      let filtered = [...state.listings]
      
      if (state.filters.category) {
        filtered = filtered.filter(listing => listing.category === state.filters.category)
      }
      
      if (state.filters.search) {
        const searchTerm = state.filters.search.toLowerCase()
        filtered = filtered.filter(listing => 
          listing.title.toLowerCase().includes(searchTerm) ||
          listing.description.toLowerCase().includes(searchTerm)
        )
      }
      
      return filtered
    },

    featuredListings: (state) => {
      return state.listings.filter(listing => listing.is_featured).slice(0, 6)
    },

    recentListings: (state) => {
      return [...state.listings]
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 8)
    }
  },

  actions: {
    async fetchListings(params = {}) {
      this.loading = true
      this.error = null
      
      try {
        const queryParams = {
          ...this.filters,
          ...params,
          page: this.pagination.page,
          page_size: this.pagination.pageSize
        }
        
        const response = await listingsAPI.getListings(queryParams)
        
        if (response.message) {
          this.listings = response.message.listings
          this.pagination = {
            page: response.message.page,
            pageSize: response.message.page_size,
            totalCount: response.message.total_count,
            totalPages: response.message.total_pages
          }
        }
      } catch (error) {
        this.error = error.response?.data?.message || 'Failed to fetch listings'
        console.error('Fetch listings error:', error)
      } finally {
        this.loading = false
      }
    },

    async fetchListingDetail(id) {
      this.loading = true
      this.error = null
      
      try {
        const response = await listingsAPI.getListingDetail(id)
        
        if (response.message) {
          this.currentListing = response.message
        }
      } catch (error) {
        this.error = error.response?.data?.message || 'Failed to fetch listing details'
        console.error('Fetch listing detail error:', error)
      } finally {
        this.loading = false
      }
    },

    async createListing(listingData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await listingsAPI.createListing(listingData)
        
        if (response.message?.success) {
          // Refresh listings
          await this.fetchListings()
          return { success: true, listingId: response.message.listing_id }
        }
      } catch (error) {
        this.error = error.response?.data?.message || 'Failed to create listing'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async updateListing(id, listingData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await listingsAPI.updateListing(id, listingData)
        
        if (response.message?.success) {
          // Update current listing if it's the one being edited
          if (this.currentListing?.listing?.name === id) {
            await this.fetchListingDetail(id)
          }
          
          // Refresh listings
          await this.fetchListings()
          return { success: true }
        }
      } catch (error) {
        this.error = error.response?.data?.message || 'Failed to update listing'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async deleteListing(id) {
      this.loading = true
      this.error = null
      
      try {
        const response = await listingsAPI.deleteListing(id)
        
        if (response.message?.success) {
          // Remove from listings array
          this.listings = this.listings.filter(listing => listing.name !== id)
          return { success: true }
        }
      } catch (error) {
        this.error = error.response?.data?.message || 'Failed to delete listing'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async fetchCategories() {
      try {
        const response = await categoriesAPI.getCategories()
        
        if (response.data) {
          this.categories = response.data
        }
      } catch (error) {
        console.error('Fetch categories error:', error)
      }
    },

    setFilters(newFilters) {
      this.filters = { ...this.filters, ...newFilters }
      this.pagination.page = 1 // Reset to first page when filters change
    },

    setPage(page) {
      this.pagination.page = page
    },

    clearCurrentListing() {
      this.currentListing = null
    },

    clearError() {
      this.error = null
    }
  }
})
