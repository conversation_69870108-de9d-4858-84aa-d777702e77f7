import { defineStore } from 'pinia'
import { authAPI } from '@/services/api'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: localStorage.getItem('auth_token'),
    isAuthenticated: false,
    loading: false,
    error: null
  }),

  getters: {
    isLoggedIn: (state) => !!state.token && !!state.user,
    userRole: (state) => state.user?.roles?.[0] || 'Guest'
  },

  actions: {
    async login(credentials) {
      this.loading = true
      this.error = null
      
      try {
        const response = await authAPI.login(credentials)
        
        if (response.message) {
          // Store token and user info
          this.token = response.message.api_key || response.message.sid
          this.user = response.message.user
          this.isAuthenticated = true
          
          localStorage.setItem('auth_token', this.token)
          localStorage.setItem('user_info', JSON.stringify(this.user))
          
          return { success: true }
        } else {
          throw new Error('Invalid login response')
        }
      } catch (error) {
        this.error = error.response?.data?.message || 'Login failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async register(userData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await authAPI.register(userData)
        
        if (response.message === 'Please check your email for verification') {
          return { success: true, message: response.message }
        } else {
          return { success: true, message: 'Registration successful' }
        }
      } catch (error) {
        this.error = error.response?.data?.message || 'Registration failed'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async logout() {
      try {
        await authAPI.logout()
      } catch (error) {
        console.error('Logout error:', error)
      } finally {
        // Clear local state regardless of API call success
        this.user = null
        this.token = null
        this.isAuthenticated = false
        
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user_info')
      }
    },

    async getCurrentUser() {
      if (!this.token) return
      
      try {
        const response = await authAPI.getCurrentUser()
        this.user = response.message
        this.isAuthenticated = true
      } catch (error) {
        console.error('Get current user error:', error)
        this.logout()
      }
    },

    // Initialize auth state from localStorage
    initializeAuth() {
      const token = localStorage.getItem('auth_token')
      const userInfo = localStorage.getItem('user_info')
      
      if (token && userInfo) {
        this.token = token
        this.user = JSON.parse(userInfo)
        this.isAuthenticated = true
        
        // Verify token is still valid
        this.getCurrentUser()
      }
    },

    clearError() {
      this.error = null
    }
  }
})
