import axios from 'axios'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:8001', // Frappe development server
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/api/method/login', credentials),
  logout: () => api.post('/api/method/logout'),
  register: (userData) => api.post('/api/method/frappe.core.doctype.user.user.sign_up', userData),
  getCurrentUser: () => api.get('/api/method/frappe.auth.get_logged_user'),
}

// Listings API
export const listingsAPI = {
  getListings: (params) => api.get('/api/method/dmsoko.api.listings.get_listings', { params }),
  getListingDetail: (id) => api.get(`/api/method/dmsoko.api.listings.get_listing_detail?listing_id=${id}`),
  createListing: (data) => api.post('/api/method/dmsoko.api.listings.create_listing', { listing_data: data }),
  updateListing: (id, data) => api.post('/api/method/dmsoko.api.listings.update_listing', { 
    listing_id: id, 
    listing_data: data 
  }),
  deleteListing: (id) => api.post('/api/method/dmsoko.api.listings.delete_listing', { listing_id: id }),
  toggleWishlist: (id) => api.post('/api/method/dmsoko.api.listings.toggle_wishlist', { listing_id: id }),
}

// Categories API
export const categoriesAPI = {
  getCategories: () => api.get('/api/resource/Category?fields=["name","category_name","description","icon"]&filters=[["is_active","=",1]]'),
  getCategoryDetail: (id) => api.get(`/api/resource/Category/${id}`),
}

// Messages API
export const messagesAPI = {
  getConversations: () => api.get('/api/resource/Conversation?fields=["name","listing","buyer","seller","last_message_at"]'),
  getMessages: (conversationId) => api.get(`/api/resource/Message?filters=[["conversation","=","${conversationId}"]]&fields=["name","sender","content","sent_at","is_read"]&order_by=sent_at`),
  sendMessage: (data) => api.post('/api/resource/Message', data),
  createConversation: (data) => api.post('/api/resource/Conversation', data),
}

// User API
export const userAPI = {
  getUserProfile: (userId) => api.get(`/api/resource/User Profile/${userId}`),
  updateUserProfile: (userId, data) => api.put(`/api/resource/User Profile/${userId}`, data),
  getUserListings: (userId) => api.get(`/api/resource/Listing?filters=[["seller","=","${userId}"]]&fields=["name","title","price","status","created_at"]`),
  getWishlist: (userId) => api.get(`/api/resource/Wishlist?filters=[["user","=","${userId}"]]&fields=["name","listing","added_at"]`),
}

// File upload API
export const fileAPI = {
  uploadFile: (file) => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('is_private', 0)
    
    return api.post('/api/method/upload_file', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      }
    })
  }
}

export default api
