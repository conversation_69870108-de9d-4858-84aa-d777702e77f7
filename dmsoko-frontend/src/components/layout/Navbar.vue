<template>
  <nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <!-- Logo and brand -->
        <div class="flex items-center">
          <RouterLink to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-lg">DM</span>
            </div>
            <span class="text-xl font-bold text-gray-900">DMSoko</span>
          </RouterLink>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <RouterLink 
            to="/listings" 
            class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
          >
            Browse Listings
          </RouterLink>
          
          <RouterLink 
            to="/categories" 
            class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
          >
            Categories
          </RouterLink>

          <!-- Post Ad Button -->
          <RouterLink 
            v-if="authStore.isLoggedIn"
            to="/post-listing" 
            class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors"
          >
            Post Ad
          </RouterLink>

          <!-- User Menu -->
          <div v-if="authStore.isLoggedIn" class="relative" ref="userMenuRef">
            <button 
              @click="toggleUserMenu"
              class="flex items-center space-x-2 text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              <img 
                v-if="authStore.user?.user_image" 
                :src="authStore.user.user_image" 
                :alt="authStore.user.full_name"
                class="w-6 h-6 rounded-full"
              >
              <div v-else class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                <span class="text-xs font-medium text-gray-600">
                  {{ authStore.user?.full_name?.charAt(0) || 'U' }}
                </span>
              </div>
              <span>{{ authStore.user?.full_name || 'User' }}</span>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>

            <!-- User Dropdown Menu -->
            <div 
              v-if="showUserMenu"
              class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200"
            >
              <RouterLink 
                to="/profile" 
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                @click="showUserMenu = false"
              >
                My Profile
              </RouterLink>
              <RouterLink 
                to="/my-listings" 
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                @click="showUserMenu = false"
              >
                My Listings
              </RouterLink>
              <RouterLink 
                to="/messages" 
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                @click="showUserMenu = false"
              >
                Messages
              </RouterLink>
              <RouterLink 
                to="/wishlist" 
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                @click="showUserMenu = false"
              >
                Wishlist
              </RouterLink>
              <hr class="my-1">
              <button 
                @click="handleLogout"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                Logout
              </button>
            </div>
          </div>

          <!-- Auth Buttons -->
          <div v-else class="flex items-center space-x-4">
            <RouterLink 
              to="/login" 
              class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Login
            </RouterLink>
            <RouterLink 
              to="/register" 
              class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors"
            >
              Sign Up
            </RouterLink>
          </div>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden flex items-center">
          <button 
            @click="toggleMobileMenu"
            class="text-gray-700 hover:text-primary-600 focus:outline-none focus:text-primary-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path v-if="!showMobileMenu" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
              <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div v-if="showMobileMenu" class="md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200">
          <RouterLink 
            to="/listings" 
            class="block text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-base font-medium"
            @click="showMobileMenu = false"
          >
            Browse Listings
          </RouterLink>
          
          <RouterLink 
            to="/categories" 
            class="block text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-base font-medium"
            @click="showMobileMenu = false"
          >
            Categories
          </RouterLink>

          <div v-if="authStore.isLoggedIn" class="space-y-1">
            <RouterLink 
              to="/post-listing" 
              class="block bg-primary-600 text-white px-3 py-2 rounded-md text-base font-medium"
              @click="showMobileMenu = false"
            >
              Post Ad
            </RouterLink>
            
            <RouterLink 
              to="/profile" 
              class="block text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-base font-medium"
              @click="showMobileMenu = false"
            >
              My Profile
            </RouterLink>
            
            <RouterLink 
              to="/my-listings" 
              class="block text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-base font-medium"
              @click="showMobileMenu = false"
            >
              My Listings
            </RouterLink>
            
            <RouterLink 
              to="/messages" 
              class="block text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-base font-medium"
              @click="showMobileMenu = false"
            >
              Messages
            </RouterLink>
            
            <RouterLink 
              to="/wishlist" 
              class="block text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-base font-medium"
              @click="showMobileMenu = false"
            >
              Wishlist
            </RouterLink>
            
            <button 
              @click="handleLogout"
              class="block w-full text-left text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-base font-medium"
            >
              Logout
            </button>
          </div>

          <div v-else class="space-y-1">
            <RouterLink 
              to="/login" 
              class="block text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-base font-medium"
              @click="showMobileMenu = false"
            >
              Login
            </RouterLink>
            <RouterLink 
              to="/register" 
              class="block bg-primary-600 text-white px-3 py-2 rounded-md text-base font-medium"
              @click="showMobileMenu = false"
            >
              Sign Up
            </RouterLink>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const showMobileMenu = ref(false)
const showUserMenu = ref(false)
const userMenuRef = ref(null)

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const handleLogout = async () => {
  await authStore.logout()
  showUserMenu.value = false
  showMobileMenu.value = false
  router.push('/')
}

// Close user menu when clicking outside
const handleClickOutside = (event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target)) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
