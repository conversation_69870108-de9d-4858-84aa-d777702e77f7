<template>
  <section class="relative bg-gradient-to-br from-primary-600 to-primary-800 text-white">
    <div class="absolute inset-0 bg-black opacity-20"></div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
      <div class="text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Find What You Need,
          <span class="text-yellow-300">Sell What You Don't</span>
        </h1>
        
        <p class="text-xl md:text-2xl mb-8 text-gray-100 max-w-3xl mx-auto">
          Join thousands of buyers and sellers in the most trusted marketplace. 
          Discover great deals or turn your items into cash today.
        </p>

        <!-- Search Bar -->
        <div class="max-w-4xl mx-auto mb-8">
          <div class="bg-white rounded-lg shadow-lg p-2 flex flex-col md:flex-row gap-2">
            <div class="flex-1">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="What are you looking for?"
                class="w-full px-4 py-3 text-gray-900 placeholder-gray-500 border-0 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                @keyup.enter="handleSearch"
              >
            </div>
            
            <div class="md:w-48">
              <select
                v-model="selectedCategory"
                class="w-full px-4 py-3 text-gray-900 border-0 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">All Categories</option>
                <option 
                  v-for="category in listingsStore.categories" 
                  :key="category.name"
                  :value="category.name"
                >
                  {{ category.category_name }}
                </option>
              </select>
            </div>
            
            <button
              @click="handleSearch"
              class="bg-primary-600 text-white px-8 py-3 rounded-md font-medium hover:bg-primary-700 transition-colors flex items-center justify-center space-x-2"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              <span>Search</span>
            </button>
          </div>
        </div>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <RouterLink
            to="/listings"
            class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <span>Browse Listings</span>
          </RouterLink>
          
          <RouterLink
            to="/post-listing"
            class="bg-yellow-400 text-gray-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors inline-flex items-center justify-center space-x-2"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <span>Post Your Ad</span>
          </RouterLink>
        </div>
      </div>
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 w-20 h-20 bg-yellow-300 rounded-full opacity-20 animate-pulse"></div>
    <div class="absolute bottom-20 right-10 w-16 h-16 bg-white rounded-full opacity-10 animate-bounce"></div>
    <div class="absolute top-1/2 right-20 w-12 h-12 bg-yellow-400 rounded-full opacity-15 animate-ping"></div>
  </section>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useListingsStore } from '@/stores/listings'

const router = useRouter()
const listingsStore = useListingsStore()

const searchQuery = ref('')
const selectedCategory = ref('')

const handleSearch = () => {
  const filters = {}
  
  if (searchQuery.value.trim()) {
    filters.search = searchQuery.value.trim()
  }
  
  if (selectedCategory.value) {
    filters.category = selectedCategory.value
  }
  
  // Navigate to listings page with search filters
  router.push({
    path: '/listings',
    query: filters
  })
}
</script>
